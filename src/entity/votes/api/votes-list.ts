'use server';

// Тип для возвращаемых данных
import {dbConnect} from "@/shared/lib/database";
import {VotesSchema} from "@/entity/votes/schema";
import {PaginationList} from "@/shared/api/external";

export type VoteList = {
    _id: string;
    originalPhoneNumber: string;
    voteDate: string;
    phoneNumber?: string; // Номер телефона голосующего
    ownerId?: string; // ID хозяйства, к которому привязан голос
    updatedBy?: string; // Кто обновил данные
    createdAt: string;
    updatedAt: string;
};

// Аргументы функции
export type VotesListParams = {
    page?: number;
    size?: number;
    ownerId?: string; // Фильтрация по ID хозяйства
    phoneNumber?: string; // Фильтрация по номеру телефона
    originalPhoneNumber?: string; // Фильтрация по оригинальному номеру телефона
    excludeWithOwner?: boolean; // Исключить голоса, которые уже привязаны к хозяйству
    onlyWithPhone?: boolean; // Только голоса с номером телефона
    excludeWithPhone?: boolean;
};

export async function getVotesPaginated({
    page = 1,
    size = 20,
    ownerId,
    phoneNumber,
    originalPhoneNumber,
    excludeWithOwner = false,
    onlyWithPhone = false
}: VotesListParams): Promise<PaginationList<VoteList>> {
    await dbConnect(); // Подключение к базе

    const skip = (page - 1) * size;

    // Создаем фильтр для поиска
    // eslint-disable-next-line
    const filter: any = {};

    // Если указан ownerId, добавляем его в фильтр
    if (ownerId) {
        filter.ownerId = ownerId;
    }

    // Если указан phoneNumber, добавляем фильтрацию по номеру телефона
    if (phoneNumber) {
        // Фильтруем по номеру телефона (поиск по части номера)
        filter.phoneNumber = { $regex: phoneNumber, $options: 'i' };
    }

    // Если указан originalPhoneNumber, добавляем фильтрацию по оригинальному номеру телефона
    if (originalPhoneNumber) {
        // Фильтруем по оригинальному номеру телефона (поиск по части номера)
        filter.originalPhoneNumber = { $regex: originalPhoneNumber, $options: 'i' };
    }

    // Если нужно исключить голоса, которые уже привязаны к хозяйству
    if (excludeWithOwner) {
        filter.ownerId = { $exists: false };
    }

    // Если нужны только голоса с номером телефона
    if (onlyWithPhone) {
        filter.phoneNumber = { $exists: true, $ne: null };
    }

    const [dataRaw, total] = await Promise.all([
        VotesSchema.find(filter).sort({ voteDate: -1 }).skip(skip).limit(size || 20).lean(),
        VotesSchema.countDocuments(filter),
    ]);

    // Приводим результат к типу Vote[]
    // eslint-disable-next-line
    // @ts-ignore
    const data = dataRaw.map((doc) => ({
        _id: doc._id?.toString(),
        originalPhoneNumber: doc.originalPhoneNumber,
        voteDate: doc.voteDate || '',
        phoneNumber: doc.phoneNumber || undefined,
        ownerId: doc.ownerId ? doc.ownerId.toString() : undefined,
        updatedBy: doc.updatedBy || undefined,
        createdAt: doc.createdAt.toISOString(),
        updatedAt: doc.updatedAt.toISOString(),
    })) as VoteList[];

    return {
        data,
        total: total as number,
        totalPages: Math.ceil(total / size),
        currentPage: page as number,
    };
}

export async function getVotesList (searchParams: Promise<VotesListParams>) {
    const params = await searchParams
    return getVotesPaginated(params)
}
